import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Verify webhook signature (you should implement this based on Flowglad's documentation)
    // const signature = request.headers.get('flowglad-signature');
    // if (!verifyWebhookSignature(body, signature)) {
    //   return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    // }

    const { type, data } = body;

    // Create Supabase client for database operations
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // Use service role key for admin operations
      {
        cookies: {
          get() { return undefined; },
          set() {},
          remove() {},
        },
      }
    );

    switch (type) {
      case 'subscription.created':
      case 'subscription.updated':
        await handleSubscriptionUpdate(supabase, data);
        break;
      
      case 'subscription.canceled':
      case 'subscription.deleted':
        await handleSubscriptionCancellation(supabase, data);
        break;
      
      case 'payment.succeeded':
        await handlePaymentSuccess(supabase, data);
        break;
      
      case 'payment.failed':
        await handlePaymentFailure(supabase, data);
        break;
      
      default:
        console.log(`Unhandled webhook event type: ${type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleSubscriptionUpdate(supabase: any, data: any) {
  const { customer_email, subscription_id, status, current_period_end } = data;
  
  try {
    // Find user by email
    const { data: user } = await supabase.auth.admin.getUserByEmail(customer_email);
    
    if (!user) {
      console.error(`User not found for email: ${customer_email}`);
      return;
    }

    // Update user profile with subscription info
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: customer_email,
        subscription_status: status,
        subscription_id: subscription_id,
        current_period_end: current_period_end ? new Date(current_period_end) : null,
        is_alpha_user: true, // Mark as alpha user for early pricing
      });

    if (error) {
      console.error('Error updating profile:', error);
    } else {
      console.log(`Updated subscription for user ${customer_email}: ${status}`);
    }
  } catch (error) {
    console.error('Error handling subscription update:', error);
  }
}

async function handleSubscriptionCancellation(supabase: any, data: any) {
  const { customer_email, subscription_id } = data;
  
  try {
    // Find user by email
    const { data: user } = await supabase.auth.admin.getUserByEmail(customer_email);
    
    if (!user) {
      console.error(`User not found for email: ${customer_email}`);
      return;
    }

    // Update user profile to reflect cancellation
    const { error } = await supabase
      .from('profiles')
      .update({
        subscription_status: 'cancelled',
        current_period_end: null,
      })
      .eq('id', user.id);

    if (error) {
      console.error('Error updating profile for cancellation:', error);
    } else {
      console.log(`Cancelled subscription for user ${customer_email}`);
    }
  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
  }
}

async function handlePaymentSuccess(supabase: any, data: any) {
  const { customer_email, amount, subscription_id } = data;
  
  try {
    console.log(`Payment succeeded for ${customer_email}: $${amount/100}`);
    
    // You could log this payment or update any payment-related fields
    // For now, we'll just ensure the subscription is active
    const { data: user } = await supabase.auth.admin.getUserByEmail(customer_email);
    
    if (user) {
      await supabase
        .from('profiles')
        .update({
          subscription_status: 'active',
        })
        .eq('id', user.id);
    }
  } catch (error) {
    console.error('Error handling payment success:', error);
  }
}

async function handlePaymentFailure(supabase: any, data: any) {
  const { customer_email, subscription_id } = data;
  
  try {
    console.log(`Payment failed for ${customer_email}`);
    
    // Update subscription status to past_due
    const { data: user } = await supabase.auth.admin.getUserByEmail(customer_email);
    
    if (user) {
      await supabase
        .from('profiles')
        .update({
          subscription_status: 'past_due',
        })
        .eq('id', user.id);
    }
  } catch (error) {
    console.error('Error handling payment failure:', error);
  }
}
