import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { SupabaseProvider } from "@/components/SupabaseProvider";
import Header from "@/components/Header";
import { FlowgladProvider } from "@flowglad/nextjs";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Voice Emotion Analysis",
  description: "Analyze emotions in your voice with AI insights",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-gray-50`}
      >
        <FlowgladProvider>
          <SupabaseProvider>
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Toaster position="top-right" />
          </SupabaseProvider>
        </FlowgladProvider>
      </body>
    </html>
  );
}
