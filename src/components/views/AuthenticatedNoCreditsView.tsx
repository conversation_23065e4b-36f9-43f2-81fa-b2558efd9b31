'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useBilling } from '@flowglad/nextjs';
import VoiceRecorder from '@/components/VoiceRecorder';
import { AnalysisSettings } from '@/types/analysis';
import toast from 'react-hot-toast';

interface AuthenticatedNoCreditsViewProps {
  user: any;
  profile: any;
}

export default function AuthenticatedNoCreditsView({ user, profile }: AuthenticatedNoCreditsViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);
  const { checkout } = useBilling();
  const router = useRouter();

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  const handleSubscribe = async () => {
    try {
      await checkout({
        priceId: 'price_voice_emotion_analysis_monthly', // This would be your actual Flowglad price ID
        successUrl: `${window.location.origin}/?success=true`,
        cancelUrl: `${window.location.origin}/?canceled=true`,
      });
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error('Failed to start checkout process');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      {/* Subscription Required Banner */}
      <div className="bg-gradient-to-r from-orange-100/60 to-amber-100/60 border-b border-orange-200/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-orange-500 text-lg">😊</span>
              <div>
                <p className="text-sm font-medium text-orange-800">
                  Hey {user.email?.split('@')[0]}! You've used up your 10 free tries
                </p>
                <p className="text-xs text-orange-600 font-light">
                  Ready to keep exploring your voice? Let's get you unlimited access!
                </p>
              </div>
            </div>
            <button
              onClick={handleSubscribe}
              className="bg-gradient-to-r from-orange-400 to-amber-500 text-white px-6 py-2 rounded-2xl hover:from-orange-500 hover:to-amber-600 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
            >
              Go Unlimited!
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Subscription Prompt */}
        <div className="max-w-2xl mx-auto text-center mb-8">
          <div className="bg-white/70 backdrop-blur-sm rounded-3xl shadow-lg border border-white/20 p-10">
            <div className="text-5xl mb-6 opacity-80">🚀</div>
            <h2 className="text-3xl font-light text-gray-800 mb-6">
              Ready to dive deeper? 🌊
            </h2>
            <p className="text-gray-500 mb-8 font-light leading-relaxed">
              Let's keep exploring your voice together with unlimited access
            </p>

            {/* Pricing Card */}
            <div className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-2xl p-8 mb-8 border border-blue-100/30">
              <div className="flex items-center justify-center mb-6">
                <span className="bg-blue-100/70 text-blue-700 text-xs font-medium px-4 py-2 rounded-full">
                  Alpha User Pricing
                </span>
              </div>
              <div className="text-4xl font-light text-gray-800 mb-4">
                $20<span className="text-xl font-light text-gray-500">/month</span>
              </div>
              <ul className="text-sm text-gray-600 space-y-2 mb-6">
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Unlimited voice analysis
                </li>
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Analysis history & insights
                </li>
                <li className="flex items-center">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Generate access codes for friends
                </li>

              </ul>
              <button
                onClick={handleSubscribe}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors font-medium"
              >
                Start Alpha Subscription
              </button>
            </div>

            <p className="text-xs text-gray-500">
              Cancel anytime • No long-term commitment • Alpha pricing guaranteed for early users
            </p>
          </div>
        </div>

        {/* Disabled Voice Recorder (for preview) */}
        <div className="opacity-50 pointer-events-none">
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        </div>
      </div>
    </div>
  );
}
