'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useBilling } from '@flowglad/nextjs';
import VoiceRecorder from '@/components/VoiceRecorder';
import ResultsPage from '@/components/ResultsPage';
import { AnalysisSettings } from '@/types/analysis';
import toast from 'react-hot-toast';

interface AuthenticatedWithFreeUsagesViewProps {
  user: any;
  profile: any;
  freeUsagesRemaining: number;
}

export default function AuthenticatedWithFreeUsagesView({
  user,
  profile,
  freeUsagesRemaining
}: AuthenticatedWithFreeUsagesViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);
  const { checkout } = useBilling();
  const router = useRouter();

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  const handleSubscribe = async () => {
    try {
      await checkout({
        priceId: 'price_voice_emotion_analysis_monthly', // This would be your actual Flowglad price ID
        successUrl: `${window.location.origin}/?success=true`,
        cancelUrl: `${window.location.origin}/?canceled=true`,
      });
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error('Failed to start checkout process');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      {/* Free Usages Banner */}
      <div className="bg-gradient-to-r from-orange-100/60 to-amber-100/60 border-b border-orange-200/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-orange-500 text-lg">🎉</span>
              <div>
                <p className="text-sm font-medium text-orange-800">
                  Hey {user.email?.split('@')[0]}! Welcome aboard!
                </p>
                <p className="text-xs text-orange-600 font-light">
                  You've got {freeUsagesRemaining} of your 10 free voice analyses remaining
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-xs text-orange-600 bg-orange-100/70 px-3 py-1 rounded-full font-medium">
                {freeUsagesRemaining} free left
              </span>
              <button
                onClick={handleSubscribe}
                className="bg-gradient-to-r from-orange-400 to-amber-500 text-white px-4 py-2 rounded-2xl hover:from-orange-500 hover:to-amber-600 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
              >
                Go Unlimited
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {!audioBlob ? (
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        ) : (
          <ResultsPage audioBlob={audioBlob} settings={analysisSettings!} />
        )}
      </div>
    </div>
  );
}
