import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./db/schema";
import { createClient } from "@supabase/supabase-js";
import { createServerClient } from "@supabase/ssr";

// Supabase client for auth (client-side)
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Server-side Supabase client with service role key for admin operations
export const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY ||
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// For now, we'll use the Supabase client directly for database operations
// This ensures RLS is properly enforced through Supabase's auth system
export const db = supabase;

// Helper function to get authenticated Supabase client for API routes
export async function getDbForRequest(request?: Request) {
  if (!request) {
    return supabase;
  }

  try {
    // Create a server-side Supabase client that can read cookies
    const cookieHeader = request.headers.get("cookie") || "";

    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            // Parse cookies from the request header
            const cookies = cookieHeader.split(";").reduce((acc, cookie) => {
              const [key, value] = cookie.trim().split("=");
              if (key && value) {
                acc[key] = decodeURIComponent(value);
              }
              return acc;
            }, {} as Record<string, string>);
            return cookies[name];
          },
          set() {
            // No-op for API routes - we don't set cookies in API responses
          },
          remove() {
            // No-op for API routes
          },
        },
      }
    );

    return serverSupabase;
  } catch (error) {
    console.error("Error creating authenticated Supabase client:", error);
    return supabase;
  }
}

// Direct Drizzle client for when we need raw SQL operations
const connectionString = process.env.DATABASE_URL!;
const postgresClient = postgres(connectionString, { prepare: false });
export const drizzleDb = drizzle(postgresClient, { schema });
