import { test, expect } from '@playwright/test';

test.describe('Voice Emotion Analysis App', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3002');
  });

  test('should display authenticated user interface correctly', async ({ page }) => {
    // Verify the page loads
    await expect(page).toHaveTitle('Voice Emotion Analysis');
    
    // Check for authenticated user elements
    await expect(page.getByText('Hey jaco<PERSON><PERSON><PERSON>! Welcome aboard!')).toBeVisible();
    await expect(page.getByText('You\'ve got 10 of your 10 free voice analyses remaining')).toBeVisible();
    
    // Verify navigation is present
    await expect(page.getByRole('link', { name: 'Home' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'History' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Account' })).toBeVisible();
    
    // Verify user menu is present
    await expect(page.getByRole('button', { name: '<PERSON>@gmail.com' })).toBeVisible();
  });

  test('should navigate between pages correctly', async ({ page }) => {
    // Test History page navigation
    await page.getByRole('link', { name: 'History' }).click();
    await expect(page).toHaveURL('http://localhost:3002/history');
    await expect(page.getByRole('heading', { name: 'Analysis History' })).toBeVisible();
    await expect(page.getByText('View and manage your past voice emotion analyses')).toBeVisible();
    
    // Test Account page navigation
    await page.getByRole('link', { name: 'Account' }).click();
    await expect(page).toHaveURL('http://localhost:3002/account');
    
    // Test Home page navigation
    await page.getByRole('link', { name: 'Home' }).click();
    await expect(page).toHaveURL('http://localhost:3002/');
  });

  test('should display voice recording interface', async ({ page }) => {
    // Verify voice recording elements are present
    await expect(page.getByRole('heading', { name: /Voice Emotion Analysis/ })).toBeVisible();
    await expect(page.getByText('📝 Do a voice journal about what\'s alive for you')).toBeVisible();
    
    // Check for microphone access request (expected in testing environment)
    await expect(page.getByRole('heading', { name: 'Microphone Access Required' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Check Permission Again' })).toBeVisible();
  });

  test('should handle user menu dropdown correctly', async ({ page }) => {
    // Click user menu button
    await page.getByRole('button', { name: 'J <EMAIL>' }).click();
    
    // Verify dropdown appears
    await expect(page.getByRole('button', { name: 'Sign Out' })).toBeVisible();
    
    // Test click outside to close dropdown
    await page.click('body');
    await expect(page.getByRole('button', { name: 'Sign Out' })).not.toBeVisible();
  });

  test('should navigate to billing page from Go Unlimited button', async ({ page }) => {
    // Click Go Unlimited button
    await page.getByRole('button', { name: 'Go Unlimited' }).click();
    
    // Verify navigation to billing page
    await expect(page).toHaveURL('http://localhost:3002/billing');
    await expect(page.getByRole('heading', { name: 'Billing & Subscription' })).toBeVisible();
    await expect(page.getByText('Manage your subscription and billing information')).toBeVisible();
    
    // Verify pricing information is displayed
    await expect(page.getByText('$20')).toBeVisible();
    await expect(page.getByText('/month')).toBeVisible();
    await expect(page.getByText('Unlimited voice emotion analysis')).toBeVisible();
  });

  test('should display empty state in history page', async ({ page }) => {
    // Navigate to history page
    await page.getByRole('link', { name: 'History' }).click();
    
    // Verify empty state is displayed
    await expect(page.getByRole('heading', { name: 'No analyses yet' })).toBeVisible();
    await expect(page.getByText('Get started by creating your first voice emotion analysis.')).toBeVisible();
    await expect(page.getByRole('link', { name: 'Start Analysis' })).toBeVisible();
  });

  test('should maintain authentication state across page navigation', async ({ page }) => {
    // Start on home page
    await expect(page.getByText('Hey jacobwaldor! Welcome aboard!')).toBeVisible();
    
    // Navigate to different pages and verify user remains authenticated
    await page.getByRole('link', { name: 'History' }).click();
    await expect(page.getByRole('button', { name: 'J <EMAIL>' })).toBeVisible();
    
    await page.getByRole('link', { name: 'Account' }).click();
    await expect(page.getByRole('button', { name: 'J <EMAIL>' })).toBeVisible();
    
    await page.getByRole('button', { name: 'Go Unlimited' }).click();
    await expect(page.getByRole('button', { name: 'J <EMAIL>' })).toBeVisible();
  });

  test('should handle responsive design elements', async ({ page }) => {
    // Test that key elements are visible and accessible
    await expect(page.getByRole('banner')).toBeVisible();
    await expect(page.getByRole('main')).toBeVisible();
    await expect(page.getByRole('navigation')).toBeVisible();
    
    // Verify interactive elements are clickable
    await expect(page.getByRole('button', { name: 'Go Unlimited' })).toBeEnabled();
    await expect(page.getByRole('button', { name: 'Check Permission Again' })).toBeEnabled();
  });
});
